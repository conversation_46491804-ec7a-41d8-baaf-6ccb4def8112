/* eslint-disable */
import { create } from 'zustand'

const useParticipantColorsStore = create((set, get) => ({
  participantColors: new Map(),
  setParticipantColors: (colors) => {
    let colorMap
    if (colors instanceof Map) {
      colorMap = colors
    } else if (typeof colors === 'function') {
      const currentColors = get().participantColors
      const newColors = colors(currentColors)
      colorMap = newColors instanceof Map ? newColors : new Map()
    } else if (colors && typeof colors === 'object') {
      try {
        colorMap = new Map(Object.entries(colors))
      } catch (error) {
        colorMap = new Map()
      }
    } else {
      colorMap = new Map()
    }
    
    set({ participantColors: colorMap })
  }
}))

export const useParticipantColors = () => {
  const store = useParticipantColorsStore()
  const participantColors = store.participantColors instanceof Map 
    ? store.participantColors 
    : new Map()
  
  return {
    participantColors,
    setParticipantColors: store.setParticipantColors
  }
}

export default useParticipantColorsStore
