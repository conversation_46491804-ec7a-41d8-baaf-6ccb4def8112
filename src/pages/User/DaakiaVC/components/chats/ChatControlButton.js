// import { <PERSON><PERSON><PERSON>og<PERSON> } from "@livekit/components-react";
import React from "react";
import { DrawerState } from "../../utils/constants";
import { ReactComponent as ChatIcon } from "../../assets/icons/ChatIcon.svg";
import { useVideoConferencesContext } from "../../context/VideoConferencesContext";

export default function ChatControlButton({
  showIcon = true,
  showText = false,
}) {
  const { setOpenDrawer } = useVideoConferencesContext();
  return (
    <div
      className="lk-button control-bar-button-icon control-bar-button chat-icon"
      onClick={() => {
        setOpenDrawer((prev) => prev === DrawerState.CHAT ? DrawerState.NONE : DrawerState.CHAT);
      }}
    >
      {showIcon && <ChatIcon />}
      {showText && "Chat"}
    </div>
  );
}
