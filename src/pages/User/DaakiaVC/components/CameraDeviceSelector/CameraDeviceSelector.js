import { useCallback, useRef, useEffect } from 'react';
import { MediaDeviceMenu } from '@livekit/components-react';
import { ReactComponent as VideoSwitch } from '../../assets/icons/videoSwitch.svg';

/**
 * CameraDeviceSelector Component
 * Reusable component for camera switching with automatic mirroring logic
 *

 * @param {function} setDeviceIdVideo - Function to set video device ID
 * @param {boolean} isSelfVideoMirrored - Current mirroring state
 * @param {function} setIsSelfVideoMirrored - Function to set mirroring state
 * @param {any} frontCameraMirroringPreference - Front camera mirroring preference
 * @param {function} setFrontCameraMirroringPreference - Function to set front camera preference
 * @param {string} className - Optional CSS class for styling
 * @param {object} style - Optional inline styles
 */
function CameraDeviceSelector({
  setDeviceIdVideo,
  isSelfVideoMirrored,
  setIsSelfVideoMirrored,
  frontCameraMirroringPreference,
  setFrontCameraMirroringPreference,
  className = "camera-select-icon",
  style = {}
}) {
  // Refs to avoid re-creating the callback on state changes
  const isSelfVideoMirroredRef = useRef(isSelfVideoMirrored);
  const frontCameraMirroringPreferenceRef = useRef(frontCameraMirroringPreference);

  // Cache device enumeration results for performance
  const deviceCacheRef = useRef(new Map());

  // Keep refs in sync with state
  useEffect(() => {
    isSelfVideoMirroredRef.current = isSelfVideoMirrored;
  }, [isSelfVideoMirrored]);

  useEffect(() => {
    frontCameraMirroringPreferenceRef.current = frontCameraMirroringPreference;
  }, [frontCameraMirroringPreference]);

  // Memoized device info fetcher
  const getDeviceInfo = useCallback(async (deviceId) => {
    if (deviceCacheRef.current.has(deviceId)) {
      return deviceCacheRef.current.get(deviceId);
    }

    const devices = await navigator.mediaDevices.enumerateDevices();
    const device = devices.find(d => d.kind === "videoinput" && d.deviceId === deviceId);

    if (device) {
      const isBackCamera = device.label.toLowerCase().includes('back') ||
                          device.label.toLowerCase().includes('rear') ||
                          device.label.toLowerCase().includes('environment');

      const deviceInfo = { device, isBackCamera };
      deviceCacheRef.current.set(deviceId, deviceInfo);
      return deviceInfo;
    }

    return null;
  }, []);

  const onActiveDeviceChange = useCallback((_kind, deviceId) => {
    // Set the correct video device ID
    setDeviceIdVideo(deviceId ?? "");

    // Check if the device is a back camera and handle mirroring
    if (deviceId) {
      getDeviceInfo(deviceId).then(deviceInfo => {
        if (deviceInfo) {
          const { isBackCamera } = deviceInfo;

          if (isBackCamera) {
            // Store current mirroring preference before switching to back camera
            // (only store if we haven't stored it already)
            if (frontCameraMirroringPreferenceRef.current === undefined) {
              setFrontCameraMirroringPreference(isSelfVideoMirroredRef.current);
            }
            // Force mirroring OFF for back cameras
            setIsSelfVideoMirrored(false);
          } else if (frontCameraMirroringPreferenceRef.current !== undefined) {
            // Front camera selected - restore the user's original front camera preference
            setIsSelfVideoMirrored(frontCameraMirroringPreferenceRef.current);
            // Clear the stored preference since we've restored it
            setFrontCameraMirroringPreference(undefined);
            // If no stored preference, don't change anything (preserve current setting)
          }
        }
      }).catch(error => {
        console.error('Error enumerating devices:', error);
        // Fallback: assume front camera, set mirroring to true
        setIsSelfVideoMirrored(true);
      });
    }
  }, [getDeviceInfo, setDeviceIdVideo, setIsSelfVideoMirrored, setFrontCameraMirroringPreference]);

  return (
    <MediaDeviceMenu
      kind="videoinput"
      onActiveDeviceChange={onActiveDeviceChange}
    >
      <div className={className} style={style}>
        <VideoSwitch />
      </div>
    </MediaDeviceMenu>
  );
}

export default CameraDeviceSelector;